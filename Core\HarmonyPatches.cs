// ────────────────────────────────────────────────────────────────────────────────
// HarmonyPatches.cs  –  R<PERSON>World 1.5+
// Harmony patches for Operation Homecoming functionality
// ────────────────────────────────────────────────────────────────────────────────

using HarmonyLib;
using <PERSON><PERSON>World;
using Verse;

namespace OperationHomecoming.Core
{
    [HarmonyPatch]
    public static class HarmonyPatches
    {
        // Patch to add CompKidnapContactTracker when a pawn becomes a prisoner
        [HarmonyPatch(typeof(Pawn_GuestTracker), nameof(Pawn_GuestTracker.SetGuestStatus))]
        [HarmonyPostfix]
        public static void SetGuestStatus_Postfix(Pawn_GuestTracker __instance, Faction newHost, GuestStatus guestStatus)
        {
            var pawn = __instance.pawn;
            if (pawn == null) return;

            // Check if pawn just became a secure prisoner (kidnapped)
            if (guestStatus == GuestStatus.Prisoner && __instance.PrisonerIsSecure && newHost != Faction.OfPlayer)
            {
                // Add the tracking component if not already present
                var comp = pawn.GetComp<CompKidnapContactTracker>();
                if (comp == null)
                {
                    // Dynamically add the component
                    comp = new CompKidnapContactTracker();
                    comp.parent = pawn;
                    comp.props = new CompProperties_KidnapContactTracker();
                    pawn.AllComps.Add(comp);
                    comp.PostSpawnSetup(false);
                    
                    Log.Message($"[OpHomecoming] Added contact tracker to kidnapped pawn: {pawn.LabelShort}");
                }
            }
        }

        // Patch to handle pawn generation for ensuring our comp is available
        [HarmonyPatch(typeof(PawnGenerator), nameof(PawnGenerator.GeneratePawn), new[] { typeof(PawnGenerationRequest) })]
        [HarmonyPostfix]
        public static void GeneratePawn_Postfix(Pawn __result, PawnGenerationRequest request)
        {
            // Only add to colonists and friendly pawns who might get kidnapped
            if (__result?.Faction == Faction.OfPlayer || 
                (__result?.Faction?.PlayerGoodwill > 0 && __result?.RaceProps?.Humanlike == true))
            {
                var comp = __result.GetComp<CompKidnapContactTracker>();
                if (comp == null)
                {
                    comp = new CompKidnapContactTracker();
                    comp.parent = __result;
                    comp.props = new CompProperties_KidnapContactTracker();
                    __result.AllComps.Add(comp);
                    comp.PostSpawnSetup(false);
                }
            }
        }

        // Patch to ensure world component is available
        [HarmonyPatch(typeof(World), nameof(World.ExposeData))]
        [HarmonyPostfix]
        public static void World_ExposeData_Postfix(World __instance)
        {
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                // Ensure our world component exists
                var component = __instance.GetComponent<WorldComponent_OperationHomecoming>();
                if (component == null)
                {
                    component = new WorldComponent_OperationHomecoming(__instance);
                    __instance.components.Add(component);
                }
            }
        }

        // Patch to handle quest completion
        [HarmonyPatch(typeof(Quest), nameof(Quest.End))]
        [HarmonyPrefix]
        public static void Quest_End_Prefix(Quest __instance, QuestEndOutcome outcome)
        {
            // Handle Operation Homecoming quest endings
            if (__instance.root?.defName == "OH_RescueCaptive")
            {
                // Find the captive pawn from quest variables
                if (__instance.QuestLookTargets.Any())
                {
                    var captivePawn = __instance.QuestLookTargets.FirstOrDefault()?.Thing as Pawn;
                    if (captivePawn != null)
                    {
                        if (outcome == QuestEndOutcome.Success)
                        {
                            OHQuestUtility.HandleQuestSuccess(__instance, captivePawn);
                        }
                        else if (outcome == QuestEndOutcome.Fail)
                        {
                            OHQuestUtility.HandleQuestFailure(__instance, captivePawn);
                        }
                    }
                }
            }
        }

        // Patch to handle site destruction
        [HarmonyPatch(typeof(Site), nameof(Site.Destroy))]
        [HarmonyPrefix]
        public static void Site_Destroy_Prefix(Site __instance)
        {
            var comp = __instance.GetComponent<KidnapCampSiteComp>();
            if (comp?.CaptivePawn != null)
            {
                Log.Message($"[OpHomecoming] Kidnap camp destroyed, handling failure for {comp.CaptivePawn.LabelShort}");
                
                // This will trigger quest failure through the quest system
                Find.SignalManager.SendSignal(new Signal("site.Destroyed", __instance.Named("SITE")));
            }
        }

        // Patch to handle map generation for sites
        [HarmonyPatch(typeof(Site), nameof(Site.GetComponent), new[] { typeof(System.Type) })]
        [HarmonyPostfix]
        public static void Site_GetComponent_Postfix(Site __instance, System.Type compType, WorldObjectComp __result)
        {
            // Ensure kidnap camp sites have our component
            if (compType == typeof(KidnapCampSiteComp) && __result == null && 
                __instance.parts.Any(p => p.def.defName == "OH_KidnapCamp"))
            {
                var comp = new KidnapCampSiteComp();
                comp.parent = __instance;
                __instance.comps.Add(comp);
            }
        }

        // Patch to handle prisoner status changes
        [HarmonyPatch(typeof(Pawn_GuestTracker), "set_PrisonerIsSecure")]
        [HarmonyPostfix]
        public static void PrisonerIsSecure_Postfix(Pawn_GuestTracker __instance, bool value)
        {
            var pawn = __instance.pawn;
            if (pawn == null) return;

            var comp = pawn.GetComp<CompKidnapContactTracker>();
            if (comp != null)
            {
                // If pawn is no longer a secure prisoner, they're no longer kidnapped
                if (!value || !pawn.IsPrisoner)
                {
                    // Stop tracking
                    Log.Message($"[OpHomecoming] Stopped tracking {pawn.LabelShort} - no longer kidnapped");
                }
            }
        }
    }
}
