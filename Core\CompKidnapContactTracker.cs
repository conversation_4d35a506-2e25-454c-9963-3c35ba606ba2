// ────────────────────────────────────────────────────────────────────────────────
// CompKidnapContactTracker.cs  –  RimWorld 1.5+
// Tracks kidnapped pawns and handles contact attempts based on skills
// ────────────────────────────────────────────────────────────────────────────────

using System;
using System.Collections.Generic;
using RimWorld;
using UnityEngine;
using Verse;

namespace OperationHomecoming.Core
{
    public class CompKidnapContactTracker : ThingComp
    {
        private int nextAttemptTick = -1;
        private int attemptIndex = 0;
        private int daysCaptured = 0;
        private SkillDef highestSkillCache;
        private int highestSkillLevelCache = 0;
        private bool hasBeenKidnapped = false;

        public CompProperties_KidnapContactTracker Props => (CompProperties_KidnapContactTracker)props;

        public override void PostSpawnSetup(bool respawningAfterLoad)
        {
            base.PostSpawnSetup(respawningAfterLoad);
            if (!respawningAfterLoad && parent is Pawn pawn && pawn.IsKidnapped())
            {
                InitializeTracking();
            }
        }

        public override void CompTick()
        {
            if (Find.TickManager.TicksGame % GenDate.TicksPerDay != 0) return;

            var pawn = parent as Pawn;
            if (pawn == null) return;

            // Check if pawn just became kidnapped
            if (pawn.IsKidnapped() && !hasBeenKidnapped)
            {
                InitializeTracking();
                return;
            }

            // If no longer kidnapped, stop tracking
            if (!pawn.IsKidnapped())
            {
                hasBeenKidnapped = false;
                return;
            }

            // Continue tracking if kidnapped
            if (hasBeenKidnapped)
            {
                daysCaptured++;

                if (Find.TickManager.TicksGame >= nextAttemptTick)
                {
                    RollContact(pawn);
                    ScheduleNextAttempt();
                }
            }
        }

        private void InitializeTracking()
        {
            hasBeenKidnapped = true;
            daysCaptured = 0;
            attemptIndex = 0;
            CacheHighestSkill();
            ScheduleNextAttempt();

            Log.Message($"[OpHomecoming] Started tracking {parent.LabelShort} - highest skill: {highestSkillCache?.label} ({highestSkillLevelCache})");
        }

        private void ScheduleNextAttempt()
        {
            var settings = OperationHomecomingMod.Settings;
            int delayDays;

            if (attemptIndex == 0)
            {
                // First attempt uses initial delay range
                delayDays = Rand.RangeInclusive(settings.contactAttemptDelayMinDays, settings.contactAttemptDelayMaxDays);
            }
            else
            {
                // Subsequent attempts use interval
                delayDays = settings.attemptIntervalDays;
            }

            nextAttemptTick = Find.TickManager.TicksGame + (delayDays * GenDate.TicksPerDay);
        }

        private void CacheHighestSkill()
        {
            var pawn = parent as Pawn;
            if (pawn?.skills?.skills == null) return;

            highestSkillCache = null;
            highestSkillLevelCache = 0;

            foreach (var skillRecord in pawn.skills.skills)
            {
                if (skillRecord.Level > highestSkillLevelCache)
                {
                    highestSkillLevelCache = skillRecord.Level;
                    highestSkillCache = skillRecord.def;
                }
            }
        }

        private void RollContact(Pawn pawn)
        {
            attemptIndex++;

            var settings = OperationHomecomingMod.Settings;
            float successChance = CalculateSuccessChance();

            Log.Message($"[OpHomecoming] {pawn.LabelShort} contact attempt #{attemptIndex}, chance: {successChance:F1}%");

            if (Rand.Chance(successChance / 100f))
            {
                // Success - emit signal for quest creation
                Find.SignalManager.SendSignal(new Signal("Signal_ContactSuccess", pawn.Named("PAWN")));
                Log.Message($"[OpHomecoming] Contact successful for {pawn.LabelShort}!");
            }
            else
            {
                // Failure - emit signal for rumor/investigation
                Find.SignalManager.SendSignal(new Signal("Signal_ContactFailed", pawn.Named("PAWN"), attemptIndex.Named("ATTEMPT")));
                Log.Message($"[OpHomecoming] Contact failed for {pawn.LabelShort}");
            }
        }

        private float CalculateSuccessChance()
        {
            var settings = OperationHomecomingMod.Settings;

            float baseChance = settings.baseSuccessPercent;
            float skillBonus = 0f;

            if (highestSkillCache != null && highestSkillLevelCache > 0)
            {
                // +10% per 4 skill levels by default
                skillBonus = (highestSkillLevelCache / 4f) * settings.perSkillIncrementPer4pts;
            }

            float totalChance = baseChance + skillBonus;
            return Math.Min(totalChance, settings.maxSuccessPercent);
        }

        public override void PostExposeData()
        {
            base.PostExposeData();
            Scribe_Values.Look(ref nextAttemptTick, "nextAttemptTick", -1);
            Scribe_Values.Look(ref attemptIndex, "attemptIndex", 0);
            Scribe_Values.Look(ref daysCaptured, "daysCaptured", 0);
            Scribe_Values.Look(ref hasBeenKidnapped, "hasBeenKidnapped", false);
            Scribe_Values.Look(ref highestSkillLevelCache, "highestSkillLevelCache", 0);
            Scribe_Defs.Look(ref highestSkillCache, "highestSkillCache");
        }

        public override string CompInspectStringExtra()
        {
            if (!hasBeenKidnapped) return null;

            var settings = OperationHomecomingMod.Settings;
            int daysUntilNext = Mathf.Max(0, (nextAttemptTick - Find.TickManager.TicksGame) / GenDate.TicksPerDay);

            return $"Days captive: {daysCaptured}\nNext contact attempt: {daysUntilNext} days\nAttempts made: {attemptIndex}";
        }
    }

    public class CompProperties_KidnapContactTracker : CompProperties
    {
        public CompProperties_KidnapContactTracker()
        {
            compClass = typeof(CompKidnapContactTracker);
        }
    }
}

namespace OperationHomecoming
{
    public static class PawnExtensions
    {
        /// <summary>Returns true if pawn is a prisoner of a non-player faction (i.e., kidnapped).</summary>
        public static bool IsKidnapped(this Pawn pawn)
        {
            return pawn.IsPrisoner && pawn.Faction != Faction.OfPlayer;
        }
    }
}
