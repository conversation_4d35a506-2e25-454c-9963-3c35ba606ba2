// ────────────────────────────────────────────────────────────────────────────────
// OHIncidentWorker_ReturnRaid.cs  –  RimWorld 1.5+
// <PERSON><PERSON> raids where the kidnapped pawn returns as part of the attacking force
// ────────────────────────────────────────────────────────────────────────────────

using System.Collections.Generic;
using System.Linq;
using RimWorld;
using Verse;
using Verse.AI.Group;

namespace OperationHomecoming.Core
{
    public class OHIncidentWorker_ReturnRaid : IncidentWorker_RaidEnemy
    {
        private Pawn returnedCaptive;

        protected override bool TryExecuteWorker(IncidentParms parms)
        {
            // Get the returned captive from parms
            if (parms.target is Map map && parms.faction != null)
            {
                // Store reference for later use
                if (parms.pawnGroups?.FirstOrDefault()?.pawns?.Any() == true)
                {
                    returnedCaptive = parms.pawnGroups.First().pawns.FirstOrDefault(p => p.Faction == Faction.OfPlayer);
                }
            }

            // Execute the base raid
            bool result = base.TryExecuteWorker(parms);
            
            if (result && returnedCaptive != null)
            {
                HandleReturnedCaptive(returnedCaptive, parms);
            }

            return result;
        }

        protected override void ResolveRaidPoints(IncidentParms parms)
        {
            base.ResolveRaidPoints(parms);
            
            // Slightly reduce raid points since we're adding a potentially friendly pawn
            parms.points *= 0.9f;
        }

        protected override List<Pawn> SpawnThreats(IncidentParms parms)
        {
            var raiders = base.SpawnThreats(parms);
            
            // Add the returned captive to the raid
            if (returnedCaptive != null)
            {
                AddReturnedCaptiveToRaid(returnedCaptive, raiders, parms);
            }
            
            return raiders;
        }

        private void AddReturnedCaptiveToRaid(Pawn captive, List<Pawn> raiders, IncidentParms parms)
        {
            if (!(parms.target is Map map)) return;

            // Change the captive's faction temporarily
            var originalFaction = captive.Faction;
            captive.SetFaction(parms.faction);
            
            // Apply captivity consequences
            ApplyCaptivityEffects(captive);
            
            // Spawn the captive with the raiders
            var spawnSpot = FindSpawnSpot(map, raiders);
            if (spawnSpot.IsValid)
            {
                GenSpawn.Spawn(captive, spawnSpot, map);
                raiders.Add(captive);
                
                // Add to the raid group
                var lord = raiders.FirstOrDefault()?.GetLord();
                if (lord != null)
                {
                    lord.AddPawn(captive);
                }
                
                // Set up loyalty check
                ScheduleLoyaltyCheck(captive, originalFaction);
                
                Log.Message($"[OpHomecoming] {captive.LabelShort} has returned as part of a raid!");
            }
        }

        private void ApplyCaptivityEffects(Pawn captive)
        {
            // Apply some negative effects from captivity
            var settings = OperationHomecomingMod.Settings;
            
            // Add some injuries
            if (Rand.Chance(0.7f))
            {
                var injury = HediffMaker.MakeHediff(HediffDefOf.Bruise, captive);
                captive.health.AddHediff(injury, captive.health.hediffSet.GetRandomNotMissingPart(DamageDefOf.Blunt));
            }
            
            // Add malnutrition
            if (Rand.Chance(0.5f))
            {
                var malnutrition = HediffMaker.MakeHediff(HediffDefOf.Malnutrition, captive);
                malnutrition.Severity = Rand.Range(0.1f, 0.4f) * settings.illnessSeverityMultiplier;
                captive.health.AddHediff(malnutrition);
            }
            
            // Change clothing to raider gear
            ChangeToRaiderClothing(captive);
        }

        private void ChangeToRaiderClothing(Pawn captive)
        {
            // Remove current apparel
            var apparelToRemove = captive.apparel.WornApparel.ToList();
            foreach (var apparel in apparelToRemove)
            {
                captive.apparel.Remove(apparel);
            }
            
            // Add basic raider clothing
            var raiderOutfit = new[]
            {
                ThingDefOf.Apparel_Pants,
                ThingDefOf.Apparel_BasicShirt,
                ThingDefOf.Apparel_Jacket
            };
            
            foreach (var apparelDef in raiderOutfit)
            {
                if (Rand.Chance(0.8f)) // Not all pieces guaranteed
                {
                    var apparel = ThingMaker.MakeThing(apparelDef) as Apparel;
                    if (apparel != null)
                    {
                        captive.apparel.Wear(apparel);
                    }
                }
            }
        }

        private IntVec3 FindSpawnSpot(Map map, List<Pawn> raiders)
        {
            // Try to spawn near other raiders
            if (raiders.Any())
            {
                var raiderPos = raiders.First().Position;
                for (int radius = 1; radius <= 5; radius++)
                {
                    foreach (var cell in GenRadial.RadialCellsAround(raiderPos, radius, true))
                    {
                        if (cell.InBounds(map) && cell.Standable(map) && !cell.Occupied(map))
                        {
                            return cell;
                        }
                    }
                }
            }
            
            // Fallback to map edge
            return CellFinder.RandomEdgeCell(map);
        }

        private void ScheduleLoyaltyCheck(Pawn captive, Faction originalFaction)
        {
            // Schedule a loyalty check during combat
            var component = Find.World.GetComponent<WorldComponent_OperationHomecoming>();
            if (component != null)
            {
                // Check loyalty after 30-60 seconds of combat
                int checkDelay = Rand.Range(1800, 3600); // 30-60 seconds in ticks
                component.ScheduleLoyaltyCheck(captive, originalFaction, checkDelay);
            }
        }

        protected override string GetLetterLabel(IncidentParms parms)
        {
            if (returnedCaptive != null)
            {
                return "OH_ReturnRaidLabel".Translate(returnedCaptive.LabelShort);
            }
            return base.GetLetterLabel(parms);
        }

        protected override string GetLetterText(IncidentParms parms, List<Pawn> pawns)
        {
            if (returnedCaptive != null)
            {
                return "OH_ReturnRaidText".Translate(returnedCaptive.LabelShort, parms.faction.Name);
            }
            return base.GetLetterText(parms, pawns);
        }
    }

    // Extension to WorldComponent for loyalty checks
    public partial class WorldComponent_OperationHomecoming
    {
        private List<ScheduledLoyaltyCheck> scheduledLoyaltyChecks = new List<ScheduledLoyaltyCheck>();

        public void ScheduleLoyaltyCheck(Pawn pawn, Faction originalFaction, int delayTicks)
        {
            scheduledLoyaltyChecks.Add(new ScheduledLoyaltyCheck
            {
                pawn = pawn,
                originalFaction = originalFaction,
                triggerTick = Find.TickManager.TicksGame + delayTicks
            });
        }

        public override void WorldComponentTick()
        {
            base.WorldComponentTick();
            
            // Handle loyalty checks
            for (int i = scheduledLoyaltyChecks.Count - 1; i >= 0; i--)
            {
                var check = scheduledLoyaltyChecks[i];
                if (Find.TickManager.TicksGame >= check.triggerTick)
                {
                    PerformLoyaltyCheck(check.pawn, check.originalFaction);
                    scheduledLoyaltyChecks.RemoveAt(i);
                }
            }
        }

        private void PerformLoyaltyCheck(Pawn pawn, Faction originalFaction)
        {
            if (pawn?.Spawned != true) return;

            // 25% chance to betray and switch sides
            if (Rand.Chance(0.25f))
            {
                pawn.SetFaction(originalFaction);
                
                // Remove from current lord
                pawn.GetLord()?.Notify_PawnLost(pawn, PawnLostCondition.ChangedFaction);
                
                // Send message
                Messages.Message(
                    "OH_LoyaltyBetrayalMessage".Translate(pawn.LabelShort),
                    pawn,
                    MessageTypeDefOf.PositiveEvent
                );
                
                Log.Message($"[OpHomecoming] {pawn.LabelShort} betrayed their captors and rejoined the colony!");
            }
        }

        public override void ExposeData()
        {
            base.ExposeData();
            Scribe_Collections.Look(ref scheduledLoyaltyChecks, "scheduledLoyaltyChecks", LookMode.Deep);
            
            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                scheduledLoyaltyChecks ??= new List<ScheduledLoyaltyCheck>();
                scheduledLoyaltyChecks.RemoveAll(c => c.pawn == null);
            }
        }

        private class ScheduledLoyaltyCheck : IExposable
        {
            public Pawn pawn;
            public Faction originalFaction;
            public int triggerTick;

            public void ExposeData()
            {
                Scribe_References.Look(ref pawn, "pawn");
                Scribe_References.Look(ref originalFaction, "originalFaction");
                Scribe_Values.Look(ref triggerTick, "triggerTick");
            }
        }
    }
}
